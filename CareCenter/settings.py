import os
from pathlib import Path
from botocore.config import Config


# ----------------------- 自定义环境参数 -----------------------
# 新增的 env 参数在这个区域引入
# 短信服务
ALIBABA_CLOUD_ACCESS_KEY_ID = os.environ.get('ALIYUN_ACCESS_KEY_ID')
ALIBABA_CLOUD_ACCESS_KEY_SECRET = os.environ.get('ALIYUN_ACCESS_KEY_SECRET')
SMS_SIGN_NAME = os.environ.get('ALIYUN_SMS_SIGN_NAME')
SMS_TEMPLATE_CODE = os.environ.get('ALIYUN_SMS_TEMPLATE_CODE')

# 是否使用PostgreSQL数据库
USE_POSTGRES_DB = os.environ.get('USE_POSTGRES_DB', 0) == '1'
# 是否为生产环境
IS_PRODUCTION = os.environ.get('IS_PRODUCTION', '0') == '1'
# 主机地址
HOST_URL = os.environ.get('HOST_URL')

# Wechat AppID
WECHAT_APPID = os.environ.get('WECHAT_APPID')
# Wechat AppSecret
WECHAT_APPSECRET = os.environ.get('WECHAT_APPSECRET')

# 文件不存在页面
FILE_NOT_FOUND_URL = f"{os.environ.get('HOST_URL')}message/file_error/"


# ----------------------- 自定义环境参数 -----------------------



# 项目根目录
BASE_DIR = Path(__file__).resolve().parent.parent

# 项目密钥
SECRET_KEY = 'django-insecure-+-6x-h_)brgv-9uwo$b@h0n(--^1!48v^65td*j_vt+ma=@32='

# 是否为开发环境
DEBUG = not IS_PRODUCTION

# 允许的主机
ALLOWED_HOSTS = [
    'localhost', '127.0.0.1', "carecenter.plancktec.com","**************","quinlivanner.mountex.online","care-center.backend.plancktec.com"]

# CSRF
CSRF_TRUSTED_ORIGINS = ['http://**************:7777','http://quinlivanner.mountex.online:7777','https://care-center.backend.plancktec.com']

# 跨域
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://localhost:3001",
    "http://localhost:5173",
    "http://localhost:5174",
    "http://localhost:63342",
    "http://127.0.0.1:8000",
    "https://care-center.backend.plancktec.com",
    "https://carecenter.plancktec.com"
]

# 安装的应用
INSTALLED_APPS = [
    'simpleui',
    'corsheaders',
    'django_celery_beat',
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'rest_framework',
    # 自定义应用
    'user',
    'file',
    'message',
    'hospital',
    'permissions',
    'maternity_center',
    'organizational_management.charge',
    'organizational_management.questionnaire',
    'organizational_management.feedback',
    'organizational_management.infection_environment',
    'organizational_management.staff_schedule',
    'organizational_management.equipment',
    'customer_service.room',
    'customer_service.core_records',
    'customer_service.activity',
    'customer_service.diet',
    'customer_service.health_education',
    'customer_service.visitor',
    'overview',
    'customer_service.medical_referral',
    'customer_service.outing_management',
    'customer_service.room_change',
    'customer_service.disinfection',
    'customer_service.postpartum',
    'customer_service.ward_round',
    'customer_service.checkout',
    'wx_mom',
    'audit_log',
    'changelog',
    'todo_list',
]

# 中间件
MIDDLEWARE = [
    # 跨域
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

# 根URL配置
ROOT_URLCONF = 'CareCenter.urls'

# 模板
TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR, 'templates')],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

# WSGI应用
WSGI_APPLICATION = 'CareCenter.wsgi.application'


# 数据库
# https://docs.djangoproject.com/en/5.2/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}


# 密码验证
# https://docs.djangoproject.com/en/5.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# 国际化
# https://docs.djangoproject.com/en/5.2/topics/i18n/

LANGUAGE_CODE = 'zh-hans'

TIME_ZONE = 'Asia/Shanghai'

USE_I18N = True

USE_TZ = True


# 静态文件 (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.2/howto/static-files/

STATIC_URL = 'static/'

STATICFILES_DIRS = (
    os.path.join(BASE_DIR, 'static'),
)

MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

if IS_PRODUCTION:
    STATIC_ROOT = '/var/www/carecenter/static'
    MEDIA_ROOT = '/var/www/carecenter/media'

# 默认主键字段类型
# https://docs.djangoproject.com/en/5.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'



SYSTEM_NAME = 'Care Center'
SIMPLEUI_HOME_TITLE = SYSTEM_NAME
SIMPLEUI_HOME_QUICK = True
SIMPLEUI_HOME_ACTION = True
SIMPLEUI_HOME_INFO = False
SIMPLEUI_LOGO = '/static/logo.png'
SIMPLEUI_ANALYSIS = False
SIMPLEPRO_MONIT_DISPLAY = True

REST_FRAMEWORK = {
       'EXCEPTION_HANDLER': 'core.authorization.care_center_exception_handler'
   }





if DEBUG:
    INSTALLED_APPS += ['silk']
    MIDDLEWARE = ['silk.middleware.SilkyMiddleware'] + MIDDLEWARE
    SILKY_PYTHON_PROFILER = True
    SILKY_META = True
    SILKY_INTERCEPT_PERCENT = 100  
    SILKY_PYTHON_PROFILER_BINARY = True
    SILKY_META = True
    profiling_path = os.path.join(BASE_DIR, 'profiling')
    if not os.path.exists(profiling_path):
        os.makedirs(profiling_path)
    SILKY_PYTHON_PROFILER_RESULT_PATH = profiling_path
    print("SILKY ENABLED, DEBUG IS ON")
    
    


CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": "redis://127.0.0.1:6379/0",
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
        }
    }
}


# CACHES = {
#     "default": {
#         "BACKEND": "django_redis.cache.RedisCache",
#         "LOCATION": f"redis://:cZNGxir3b6paAZmX@127.0.0.1:6379/0",
#         "OPTIONS": {
#             "CLIENT_CLASS": "django_redis.client.DefaultClient",
#         }
#     }
# }


STORAGES = {
    "default": {
        # "BACKEND": "storages.backends.s3.S3Storage",
        "BACKEND": "core.storges.CustomOSSStorage",
        "OPTIONS": {
            "access_key": os.environ.get('ALIYUN_ACCESS_KEY_ID'),
            "secret_key": os.environ.get('ALIYUN_ACCESS_KEY_SECRET'),
            "bucket_name": os.environ.get('ALIYUN_OSS_BUCKET_NAME'),
            "endpoint_url": f"https://{os.environ.get('ALIYUN_OSS_ENDPOINT')}", 
            # "custom_domain": os.environ.get('ALIYUN_OSS_CUSTOM_DOMAIN'),
            "region_name": "oss-cn-hangzhou",
            "default_acl": "private",
            "querystring_auth": True,
            "file_overwrite": True,
            "url_protocol": "https:",
            "object_parameters": {
                "ContentDisposition": "inline",
                "CacheControl": "max-age=86400",
            },
            "client_config": Config(
                request_checksum_calculation="when_required",
                s3={
                    "payload_signing_enabled": False,
                    "signature_version": "s3v4", 
                    "addressing_style": "virtual", 

                }
            ),
        },
    },
    
    "staticfiles":{
        "BACKEND": "django.contrib.staticfiles.storage.ManifestStaticFilesStorage",
        "OPTIONS": {
            # collectstatic 时写入的目标目录，与你的 STATIC_ROOT 保持一致即可
            "location": BASE_DIR / "static",
            # 也可以省略，使用 storage 默认逻辑
        },
    },
    }

# ----------------------- Celery配置 -----------------------
# Celery配置
CELERY_BROKER_URL = 'redis://127.0.0.1:6379/1'  # 使用Redis数据库1，避免与缓存冲突
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = 'Asia/Shanghai'
CELERY_ENABLE_UTC = False
CELERY_BEAT_SCHEDULER = 'django_celery_beat.schedulers:DatabaseScheduler'


# Celery任务路由配置
CELERY_TASK_ROUTES = {
    'audit_log.tasks.*': {'queue': 'audit_log'},
    'core.tasks.*': {'queue': 'default'},
}

# 任务重试配置
CELERY_TASK_ANNOTATIONS = {
    '*': {'rate_limit': '60/m'},
}

# Celery 日志配置

CELERY_WORKER_HIJACK_ROOT_LOGGER = False
CELERY_WORKER_LOG_FORMAT = '[%(asctime)s: %(levelname)s/%(processName)s] %(message)s'
CELERY_WORKER_TASK_LOG_FORMAT = '[%(asctime)s: %(levelname)s/%(processName)s][%(task_name)s(%(task_id)s)] %(message)s'
# ----------------------- 日志配置 -----------------------
# LOGGING = {
#     'version': 1,
#     'disable_existing_loggers': False,
#     'formatters': {
#         'verbose': {
#             'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
#             'style': '{',
#         },
#         'simple': {
#             'format': '{levelname} {message}',
#             'style': '{',
#         },
#         'celery': {
#             'format': '[{asctime}] {levelname} {name}: {message}',
#             'style': '{',
#         },
#     },
#     'handlers': {
#         'console': {
#             'level': 'INFO',
#             'class': 'logging.StreamHandler',
#             'formatter': 'verbose',
#         },
#         'file': {
#             'level': 'INFO',
#             'class': 'logging.FileHandler',
#             'filename': os.path.join(BASE_DIR, 'logs', 'django.log'),
#             'formatter': 'verbose',
#         },
#         'celery_file': {
#             'level': 'INFO',
#             'class': 'logging.FileHandler',
#             'filename': os.path.join(BASE_DIR, 'logs', 'celery.log'),
#             'formatter': 'celery',
#         },
#         'audit_log_file': {
#             'level': 'INFO',
#             'class': 'logging.FileHandler',
#             'filename': os.path.join(BASE_DIR, 'logs', 'audit_log.log'),
#             'formatter': 'celery',
#         },
#     },
#     'loggers': {
#         'django': {
#             'handlers': ['console', 'file'],
#             'level': 'INFO',
#             'propagate': True,
#         },
#         'celery': {
#             'handlers': ['console', 'celery_file'],
#             'level': 'INFO',
#             'propagate': False,
#         },
#         'audit_log.tasks': {
#             'handlers': ['console', 'audit_log_file'],
#             'level': 'INFO',
#             'propagate': False,
#         },
#         'core.logs': {
#             'handlers': ['console', 'audit_log_file'],
#             'level': 'INFO',
#             'propagate': False,
#         },
#     },
# }

# 确保日志目录存在
logs_dir = os.path.join(BASE_DIR, 'logs')
if not os.path.exists(logs_dir):
    os.makedirs(logs_dir)

# ----------------------- Celery配置 -----------------------