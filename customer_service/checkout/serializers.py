from rest_framework import serializers

from core.parse_time import ShanghaiFriendlyDateTimeField
from customer_service.core_records.models.baby import Newborn
from customer_service.core_records.models.maternity_admission import MaternityAdmission
from customer_service.ward_round.models import MaternityWardRoundRecord, NewBornWardRoundRecord
        
        


# 在住产妇选择列表序列化器
class MaternitySelectListSerializer(serializers.ModelSerializer):
    
    maternity_name = serializers.SerializerMethodField()
    room_number = serializers.SerializerMethodField()
    check_in_date = serializers.SerializerMethodField()
    expected_checkout_date = serializers.SerializerMethodField()
    
    class Meta:
        model = MaternityAdmission
        fields = ['aid','maternity_name','room_number','check_in_date','expected_checkout_date']
    
    def get_maternity_name(self,obj):
        
        name = obj.maternity.name or '-'
        
        return f'{name}'
    
    def get_room_number(self,obj):
        
        room_number = obj.room.room_number or '-'
        
        return f'{room_number}'
    
    def get_check_in_date(self,obj):
        
        check_in_date = obj.actual_check_in_date or None
        
        return f'{check_in_date}'
    
    def get_expected_checkout_date(self,obj):
        
        expected_checkout_date = obj.expected_check_out_date or None
        
        return f'{expected_checkout_date}'
    
    
# 新生儿选择列表序列化器
class NewbornSelectListSerializer(serializers.ModelSerializer):
    
    newborn = serializers.SerializerMethodField()
    
    class Meta:
        model = Newborn
        fields = ['nid','newborn']
    
    def get_newborn(self,obj):
        return f'{obj.name}-({obj.hand_card_number or '无手卡号'})'