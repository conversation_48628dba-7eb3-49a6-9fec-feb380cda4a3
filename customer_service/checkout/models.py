from django.db import models

from core.model import BaseModel
from customer_service.checkout.enum import CheckoutStatusEnum
from customer_service.core_records.models.maternity_admission import MaternityAdmission
from maternity_center.models import MaternityCenter


# 退房管理模型
class Checkout(BaseModel):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心", null=True, blank=True)
    # 产妇入院记录
    maternity_admission = models.ForeignKey(MaternityAdmission, on_delete=models.CASCADE, verbose_name="产妇入院记录", related_name="checkout_records")
    # 实际退房日期
    actual_checkout_date = models.DateField(verbose_name="实际退房日期")
    # 设施设备检查情况
    facility_equipment_check = models.TextField(verbose_name="设施设备检查情况",  blank=True,default="")
    # 退房状态
    checkout_status = models.CharField(verbose_name="退房状态", choices=CheckoutStatusEnum.choices,  max_length=50)
    
    
    class Meta:
        verbose_name = "退房管理"
        verbose_name_plural = verbose_name
        
    
    
    
    
    
    
